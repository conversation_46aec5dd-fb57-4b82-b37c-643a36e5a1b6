{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.14/MicrosoftTeams.schema.json", "manifestVersion": "1.14", "version": "1.0.3", "id": "f984ebaf-4c50-4de8-8687-80672674ab06", "packageName": "com.contoso.voicemessaging", "developer": {"name": "Citrus Voice Message", "websiteUrl": "https://cvm.citrusinformatics.com", "privacyUrl": "https://cvm.citrusinformatics.com/privacy", "termsOfUseUrl": "https://cvm.citrusinformatics.com/terms"}, "name": {"short": "Voice Messages", "full": "Voice Messaging for Microsoft Teams"}, "description": {"short": "Send voice messages in Teams", "full": "Record and send voice messages directly within Microsoft Teams."}, "icons": {"outline": "outline.png", "color": "color.png"}, "accentColor": "#FFFFFF", "configurableTabs": [], "staticTabs": [{"entityId": "voice-messages", "name": "Voice Messages", "contentUrl": "https://cvm.citrusinformatics.com/", "websiteUrl": "https://cvm.citrusinformatics.com/", "scopes": ["personal"]}], "bots": [{"botId": "f984ebaf-4c50-4de8-8687-80672674ab06", "scopes": ["personal", "team", "groupChat"], "supportsFiles": true, "isNotificationOnly": false, "needsChannelSelector": false, "commandLists": [{"scopes": ["personal", "team", "groupChat"], "commands": [{"title": "Help", "description": "Shows help information"}]}]}], "composeExtensions": [{"botId": "f984ebaf-4c50-4de8-8687-80672674ab06", "canUpdateConfiguration": false, "commands": [{"id": "recordVoiceMessage", "context": ["compose", "commandBox", "message"], "type": "action", "title": "Voice Message", "description": "Record and send voice messages", "initialRun": true, "fetchTask": true, "taskInfo": {"title": "Record Voice Message", "width": "350", "height": "380", "url": "https://cvm.citrusinformatics.com/index.html"}, "parameters": [{"name": "voiceMessage", "title": "Voice Message", "description": "Record a voice message"}]}]}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["cvm.citrusinformatics.com", "*.citrusinformatics.com", "token.botframework.com", "*.login.microsoftonline.com", "*.sharepoint.com", "*.teams.microsoft.com", "smba.trafficmanager.net", "graph.microsoft.com", "login.microsoftonline.com"], "webApplicationInfo": {"id": "f984ebaf-4c50-4de8-8687-80672674ab06", "resource": "api://cvm.citrusinformatics.com/f984ebaf-4c50-4de8-8687-80672674ab06"}, "devicePermissions": ["media"], "showLoadingIndicator": true}