const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../dist');
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir);
}

// Create a file to stream archive data to
const output = fs.createWriteStream(path.join(outputDir, 'teams-voice-messaging.zip'));
const archive = archiver('zip', {
    zlib: { level: 9 } // Sets the compression level
});

// Listen for all archive data to be written
output.on('close', function() {
    console.log('Archive created successfully!');
    console.log('Total bytes:', archive.pointer());
});

// Good practice to catch warnings (ie stat failures and other non-blocking errors)
archive.on('warning', function(err) {
    if (err.code === 'ENOENT') {
        console.warn('Warning:', err);
    } else {
        throw err;
    }
});

// Good practice to catch this error explicitly
archive.on('error', function(err) {
    throw err;
});

// Pipe archive data to the file
archive.pipe(output);

// Add manifest.json
archive.file(path.join(__dirname, '../manifest/manifest.json'), { name: 'manifest.json' });

// Add icons
archive.file(path.join(__dirname, '../manifest/icons/color.png'), { name: 'color.png' });
archive.file(path.join(__dirname, '../manifest/icons/outline.png'), { name: 'outline.png' });

// Finalize the archive
archive.finalize(); 