# Teams Voice Messaging Extension

A Microsoft Teams extension that enables voice messaging capabilities in the desktop application.

## Features

- Record voice messages directly within Teams
- Preview recordings before sending
- Support for both light and dark Teams themes
- Automatic message delivery to recipients
- Secure storage using Azure Blob Storage
- Real-time status updates

## Prerequisites

- Node.js 14.x or later
- Microsoft 365 Developer account
- Azure subscription
- Teams admin access for deployment

## Configuration

1. Register your application in Azure Active Directory:
   - Go to Azure Portal > Azure Active Directory > App registrations
   - Create a new registration
   - Add necessary API permissions for Microsoft Graph
   - Note down the Client ID, Client Secret, and Tenant ID

2. Create an Azure Storage account:
   - Create a new storage account in Azure Portal
   - Create a blob container named 'voice-messages'
   - Note down the connection string

3. Set up environment variables:
   ```bash
   TENANT_ID=your_tenant_id
   CLIENT_ID=your_client_id
   CLIENT_SECRET=your_client_secret
   AZURE_STORAGE_CONNECTION_STRING=your_storage_connection_string
   ```

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd teams-voice-messaging
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the server:
   ```bash
   npm start
   ```

## Teams App Setup

1. Package the Teams app:
   - Update the manifest.json with your app details
   - Create a zip file containing:
     - manifest.json
     - outline.png
     - color.png

2. Upload to Teams:
   - Go to Teams Admin Center
   - Navigate to Teams apps > Manage apps
   - Click "Upload new app"
   - Select your zip file

## Usage

1. Access the voice messaging tab in Teams
2. Click the record button to start recording
3. Preview your recording
4. Send or discard the message
5. Recipients will receive a notification with the voice message

## Security Considerations

- All communication is encrypted using HTTPS
- Microsoft Graph API
- User permissions are validated for each operation

## Troubleshooting

Common issues and solutions:

1. Recording not working:
   - Check microphone permissions in browser
   - Ensure Teams has necessary permissions

2. Messages not sending:
   - Check network connectivity
   - Confirm Microsoft Graph permissions

3. App not loading:
   - Verify manifest configuration
   - Check browser console for errors
   - Confirm Teams admin approval

## Support

For issues and feature requests, please contact your Teams administrator or open an issue in the repository.

## License

